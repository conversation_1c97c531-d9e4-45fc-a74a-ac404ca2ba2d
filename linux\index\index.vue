<template>
	<page-container ref="pageContainer" :isShowNav="false" bgColorPage="#FAFAFA" @scroll="onPageScrollHandler">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-360 fixed top-0 z-10"
		/>
		<custom-nav bg-color="unset" title="" :is-back="true"> </custom-nav>
		<view class="content-wrapper px-24">
			<view class="server-info sticky top-200 z-10">
				<view class="relative flex justify-between">
					<view class="flex flex-col">
						<view class="text-36 font-bold">{{ serverDisplayInfo.name }}</view>
						<view class="text-24 py-16"
							>IP：{{ serverDisplayInfo.ip }} | {{ serverDisplayInfo.system }}</view
						>
						<view class="text-24 flex items-center">
							<view class="w-16 h-16 rd-50%" :style="{ backgroundColor: serverStatusColor }"></view>
							<text class="text-24 pl-16" :style="{ color: serverStatusColor }">{{
								serverDisplayInfo.uptime
							}}</text>
						</view>
					</view>
					<image
						src="@/static/index/server-bg.png"
						mode="aspectFit"
						class="absolute -top-130 -right-80 w-460 h-460"
						:style="serverBgStyle"
					></image>
				</view>
			</view>
			<view class="detail mt-68 pb-48 z-1">
				<function-list
					title="功能"
					:function-list="basicFunctionList"
					:show-edit="true"
					:columns="5"
					@itemClick="handleBasicFunctionClick"
					@editClick="() => handleFunctionListEdit('basic')"
				/>
				<function-list
					class="mt-24"
					title="插件"
					:function-list="pluginFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="handlePluginFunctionClick"
					@editClick="() => handleFunctionListEdit('plugin')"
				/>
				<function-list
					class="my-24"
					title="环境"
					:function-list="environmentFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="handleEnvironmentFunctionClick"
					@editClick="() => handleFunctionListEdit('environment')"
				/>
				<statusInfo title="负载" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">1分钟</text>
									<text class="text-24 font-800">{{ formatLoadValue(loadData.one) }}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">5分钟</text>
									<text class="text-24 font-800">{{ formatLoadValue(loadData.five) }}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">15分钟</text>
									<text class="text-24 font-800">{{ formatLoadValue(loadData.fifteen) }}</text>
								</view>
							</view>
							<view class="">
								<MetricProgressBar
									mode="vertical"
									:value="loadData.percentage"
									height="184rpx"
									thickness="100rpx"
									:server-status="serverDisplayInfo.isOnline"
									:animated="true"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控未开启时显示空状态 -->
							<view
								v-if="!isMonitoringEnabled"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看负载详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>

							<!-- 监控已开启但暂无数据 -->
							<view
								v-else-if="!hasLoadData"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">暂无负载数据</text>
								<text class="text-24 color-#999">监控已开启，等待数据采集中...</text>
							</view>

							<!-- 监控已开启且有数据时显示图表 -->
							<view v-else>
								<ECharts
									canvas-id="load-chart-info"
									chart-type="line"
									:chart-data="loadChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="CPU" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">核心</text>
									<text class="text-24 font-800">{{
										cpuDetailInfo.isDataValid ? cpuDetailInfo.logicalCores : 0
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">{{
										formatCpuValue(cpuDetailInfo.isDataValid ? cpuDetailInfo.usage : 0)
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">空闲</text>
									<text class="text-24 font-800">{{
										formatCpuValue(cpuDetailInfo.isDataValid ? cpuDetailInfo.idle : 0)
									}}</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="cpu-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(cpuDetailInfo.usage, 'CPU')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控未开启时显示空状态 -->
							<view
								v-if="!isMonitoringEnabled"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看CPU详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>

							<!-- 监控已开启但暂无数据 -->
							<view
								v-else-if="!hasCpuData"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">暂无CPU数据</text>
								<text class="text-24 color-#999">监控已开启，等待数据采集中...</text>
							</view>

							<!-- 监控已开启且有数据时显示详细信息 -->
							<view v-else>
								<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
									<text class="text-28 font-800">基础信息</text>
									<text class="text-24 text-secondary py-8">{{
										cpuDetailInfo.isDataValid && cpuDetailInfo.modelName
											? cpuDetailInfo.modelName
											: 'CPU 型号未知'
									}}</text>
									<text class="text-24 text-secondary">
										{{ cpuDetailInfo.isDataValid ? cpuDetailInfo.physicalCpuCount : 0 }}个物理CPU，
										{{ cpuDetailInfo.isDataValid ? cpuDetailInfo.physicalCores : 0 }}个物理核心，
										{{ cpuDetailInfo.isDataValid ? cpuDetailInfo.logicalCores : 0 }}个逻辑核心
									</text>
									<!-- 显示各核心使用率信息 -->
									<view
										v-if="
											cpuDetailInfo.isDataValid &&
											cpuDetailInfo.coreUsages &&
											cpuDetailInfo.coreUsages.length > 0
										"
										class="mt-16"
									>
										<text class="text-24 text-secondary">各核心使用率：</text>
										<view class="flex flex-wrap mt-8">
											<text
												v-for="(usage, index) in cpuDetailInfo.coreUsages"
												:key="index"
												class="text-20 text-secondary mr-16 mb-8"
											>
												核心{{ index + 1 }}: {{ usage >= 0 ? usage + '%' : '未知' }}
											</text>
										</view>
									</view>
									<!-- 数据无效时的提示 -->
									<view v-if="!cpuDetailInfo.isDataValid" class="mt-16">
										<text class="text-20 text-#999">CPU 数据解析异常，显示信息可能不准确</text>
									</view>
								</view>
								<ECharts
									canvas-id="cpu-chart-info"
									chart-type="line"
									:chart-data="cpuChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="内存" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">总计</text>
									<text class="text-24 font-800">{{
										memDetailInfo.isDataValid ? memDetailInfo.memNewTotal : '0MB'
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">{{
										formatMemoryValue(memDetailInfo.isDataValid ? memDetailInfo.memRealUsed : 0)
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">可用</text>
									<text class="text-24 font-800">{{
										formatMemoryValue(memDetailInfo.isDataValid ? memDetailInfo.memAvailable : 0)
									}}</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="memory-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(memDetailInfo.memUsagePercentage, '内存')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控未开启时显示空状态 -->
							<view
								v-if="!isMonitoringEnabled"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看内存详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>

							<!-- 监控已开启但暂无数据 -->
							<view
								v-else-if="!hasMemoryData"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">暂无内存数据</text>
								<text class="text-24 color-#999">监控已开启，等待数据采集中...</text>
							</view>

							<!-- 监控已开启且有数据时显示详细信息 -->
							<view v-else>
								<ECharts
									canvas-id="memory-chart-info"
									chart-type="line"
									:chart-data="memoryChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>
						</view>
					</template>
				</statusInfo>

				<statusInfo class="mt-24" title="磁盘" expanded-text="收起" collapsed-text="详情" @toggle="handleDiskToggle">
					<template #desc>
						<text class="text-24 text-secondary" v-if="diskDetailInfo.totalDiskCount > 1">
							若有多个磁盘，可左右滑动查看
						</text>
					</template>
					<template #basic>
						<!-- 有磁盘数据时显示 -->
						<view v-if="diskDetailInfo.isDataValid && diskDetailInfo.diskList.length > 0">
							<!-- 单个磁盘时直接显示 -->
							<view v-if="diskDetailInfo.diskList.length === 1" class="flex items-center justify-between">
								<view class="flex items-center justify-between w-60%">
									<view class="flex flex-col items-center">
										<text class="text-24 mb-16 text-secondary">总计</text>
										<text class="text-24 font-800">{{
											formatDiskSize(diskDetailInfo.diskList[0].totalSize)
										}}</text>
									</view>
									<view class="flex flex-col items-center">
										<text class="text-24 mb-16 text-secondary">已用</text>
										<text class="text-24 font-800">{{
											formatDiskSize(diskDetailInfo.diskList[0].usedSize)
										}}</text>
									</view>
									<view class="flex flex-col items-center">
										<text class="text-24 mb-16 text-secondary">空闲</text>
										<text class="text-24 font-800">{{
											formatDiskSize(diskDetailInfo.diskList[0].availableSize)
										}}</text>
									</view>
								</view>
								<view class="w-40% ml-80 mt-24">
									<MetricProgressBar
										class="z-1"
										mode="horizontal"
										:value="diskDetailInfo.diskList[0].numericUsagePercentage"
										width="100%"
										thickness="16rpx"
										:server-status="serverDisplayInfo.isOnline"
										:animated="true"
										text-position="top-right"
									/>
								</view>
							</view>

							<!-- 多个磁盘时使用 swiper -->
							<swiper v-else class="server-item h-100" @change="handleDiskSwiperChange">
								<swiper-item v-for="(disk, index) in diskDetailInfo.diskList" :key="index">
									<view class="flex items-center justify-between">
										<view class="flex items-center justify-between w-60%">
											<view class="flex flex-col items-center">
												<text class="text-24 mb-16 text-secondary">总计</text>
												<text class="text-24 font-800">{{
													formatDiskSize(disk.totalSize)
												}}</text>
											</view>
											<view class="flex flex-col items-center">
												<text class="text-24 mb-16 text-secondary">已用</text>
												<text class="text-24 font-800">{{
													formatDiskSize(disk.usedSize)
												}}</text>
											</view>
											<view class="flex flex-col items-center">
												<text class="text-24 mb-16 text-secondary">空闲</text>
												<text class="text-24 font-800">{{
													formatDiskSize(disk.availableSize)
												}}</text>
											</view>
										</view>
										<view class="w-40% ml-80 mt-24">
											<MetricProgressBar
												class="z-1"
												mode="horizontal"
												:value="disk.numericUsagePercentage"
												width="100%"
												thickness="16rpx"
												:server-status="serverDisplayInfo.isOnline"
												:animated="true"
												text-position="top-right"
											/>
										</view>
									</view>
								</swiper-item>
							</swiper>
						</view>

						<!-- 无磁盘数据时显示 -->
						<view v-else class="flex items-center justify-center py-32">
							<text class="text-24 text-secondary">暂无磁盘数据</text>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 有磁盘数据时显示详细信息 -->
							<view v-if="diskDetailInfo.isDataValid && diskDetailInfo.diskList.length > 0">
								<!-- 显示当前激活分区的详细信息 -->
								<view v-if="currentActiveDisk" class="mb-24">
									<!-- 分区标题（多个分区时显示） -->
									<view v-if="diskDetailInfo.diskList.length > 1" class="mb-16">
										<text class="text-32 font-800 color-#333">
											分区 {{ currentDiskIndex + 1 }} / {{ diskDetailInfo.diskList.length }}
										</text>
									</view>

									<!-- 基础信息 -->
									<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
										<text class="text-28 font-800 mb-16">基础信息</text>
										<view class="flex items-center py-8">
											<text class="text-24 text-secondary flex-1">
												挂载点：{{ currentActiveDisk.path
												}}{{ currentActiveDisk.rname !== currentActiveDisk.path ? `(${currentActiveDisk.rname})` : '' }}
											</text>
											<text class="text-24 text-secondary flex-1">
												文件系统：{{ currentActiveDisk.filesystem }}
											</text>
										</view>
										<view class="flex items-center">
											<text class="text-24 text-secondary flex-1">
												类型：{{ currentActiveDisk.type }}
											</text>
											<view class="flex items-center flex-1">
												<text class="text-24 text-secondary">使用率：</text>
												<text
													class="text-24 font-800 ml-8"
													:style="{ color: getDiskUsageColor(currentActiveDisk.usagePercentage) }"
												>
													{{ formatDiskPercentage(currentActiveDisk.usagePercentage) }}
												</text>
												<text
													class="text-20 ml-8"
													:style="{ color: getDiskUsageColor(currentActiveDisk.usagePercentage) }"
												>
													({{ getDiskUsageStatus(currentActiveDisk.usagePercentage) }})
												</text>
											</view>
										</view>
									</view>

									<!-- Inode 信息 -->
									<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
										<text class="text-28 font-800 mb-16">Inode信息</text>
										<view class="flex items-center py-8">
											<text class="text-24 text-secondary flex-1">
												总数：{{ currentActiveDisk.totalInodes.toLocaleString() }}
											</text>
											<text class="text-24 text-secondary flex-1">
												已用：{{ currentActiveDisk.usedInodes.toLocaleString() }}
											</text>
										</view>
										<view class="flex items-center">
											<text class="text-24 text-secondary flex-1">
												可用：{{ currentActiveDisk.availableInodes.toLocaleString() }}
											</text>
											<text class="text-24 text-secondary flex-1">
												使用率：{{ formatInodePercentage(currentActiveDisk.inodesUsagePercentage) }}
											</text>
										</view>
									</view>
								</view>

								<!-- 监控未开启时的提示 -->
								<view
									v-if="!isMonitoringEnabled"
									class="empty-state-container flex flex-col items-center justify-center text-center mt-24"
								>
									<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
									<text class="text-24 color-#999 mb-24">开启监控后可查看磁盘IO详细数据</text>
									<uv-button
										type="primary"
										size="mini"
										:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
										@click="navigateToMonitorPage"
									>
										去开启监控
									</uv-button>
								</view>

								<!-- 监控已开启但暂无数据 -->
								<view
									v-else-if="!hasDiskData"
									class="empty-state-container flex flex-col items-center justify-center text-center mt-24"
								>
									<text class="text-28 font-600 color-#333 mb-8">暂无磁盘IO数据</text>
									<text class="text-24 color-#999">监控已开启，等待数据采集中...</text>
								</view>

								<!-- 监控已开启且有数据时显示图表 -->
								<view v-else>
									<ECharts
										canvas-id="disk-chart-info"
										chart-type="line"
										:chart-data="diskChartData"
										:opts="getLoadChartStyle()"
										:height="500"
									/>
								</view>
							</view>

							<!-- 无磁盘数据时显示空状态 -->
							<view
								v-else
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">暂无磁盘数据</text>
								<text class="text-24 color-#999 mb-24">请检查服务器连接状态</text>
							</view>
						</view>
					</template>
				</statusInfo>

				<statusInfo
					class="mt-24"
					title="网络"
					expanded-text="收起"
					collapsed-text="详情"
					@toggle="handleNetworkToggle"
				>
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex items-center">
									<uv-icon name="arrow-upward" color="#3EAF3B" size="18"></uv-icon>
									<text class="text-24 ml-8 font-800 flex-1">{{ networkIoData.up }}KB/s</text>
								</view>
								<view class="flex items-center">
									<uv-icon name="arrow-downward" color="#EAD928" size="18"></uv-icon>
									<text class="text-24 ml-8 font-800 flex-1">{{ networkIoData.down }}KB/s</text>
								</view>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控未开启时显示空状态 -->
							<view
								v-if="!isMonitoringEnabled"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看网络详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>

							<!-- 监控已开启但暂无数据 -->
							<view
								v-else-if="!hasNetworkData"
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">暂无网络数据</text>
								<text class="text-24 color-#999">监控已开启，等待数据采集中...</text>
							</view>

							<!-- 监控已开启且有数据时显示图表 -->
							<view v-else>
								<ECharts
									canvas-id="network-chart-info"
									chart-type="line"
									:chart-data="newNetworkChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>
						</view>
					</template>
				</statusInfo>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { onReady, onShow, onHide, onUnload } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import FunctionList from './functionList.vue';
	import StatusInfo from './statusInfo.vue';
	import MetricProgressBar from '@/components/MetricProgressBar/index.vue';
	import {
		getLoadChartStyle,
		loadChartData,
		cpuChartData,
		getBaseChartConfig,
		chartMap,
		memoryChartData,
		diskChartData,
		newNetworkChartData,
		basicFunctionList,
		pluginFunctionList,
		environmentFunctionList,
		backgroundImage,
		backgroundImageStyle,
		serverBgStyle,
		onPageScrollHandler,
		resetPageScrollPosition,
		handleBasicFunctionClick,
		handlePluginFunctionClick,
		handleEnvironmentFunctionClick,
		handleFunctionListEdit,
		pageContainer,
		serverDisplayInfo,
		serverStatusColor,
		getNetwork,
		startTimer,
		stopTimer,
		initData,
		loadData,
		formatLoadValue,
		// 新增的监控状态相关变量和函数
		isMonitoringEnabled,
		isLoadingMonitorStatus,
		monitorStatusError,
		navigateToMonitorPage,
		checkMonitoringStatus,
		// 数据状态检查计算属性
		hasLoadData,
		hasCpuData,
		hasMemoryData,
		hasNetworkData,
		hasDiskData,
		hasAnyMonitoringData,
		// CPU 详细信息
		cpuDetailInfo,
		// CPU 数据（来自 getCpuAndMemoryData API）
		cpuData,
		formatCpuValue,
		// 内存详细信息
		memDetailInfo,
		// 内存数据（来自 getCpuAndMemoryData API）
		memoryData,
		formatMemoryValue,
		formatMemoryPercentage,
		// 网络数据
		networkIoData,
		formatNetworkData,
		handleNetworkToggle,
		// 磁盘详细信息
		handleDiskToggle,
		diskDetailInfo,
		formatDiskSize,
		formatDiskPercentage,
		formatInodePercentage,
		getDiskUsageColor,
		getDiskUsageStatus,
		currentDiskIndex,
		handleDiskSwiperChange,
		currentActiveDisk,
	} from './useController';

	// 生命周期钩子
	onReady(() => {
		// 初始化图表数据
		initData();
		getNetwork();
	});

	onShow(() => {
		// 重置页面滚动位置到顶部
		resetPageScrollPosition();
		// 检查监控状态
		checkMonitoringStatus();
		// 设置定时器
		startTimer();
	});

	onHide(() => {
		// 清除定时器
		stopTimer();
	});

	onUnload(() => {
		// 清除定时器
		stopTimer();
	});
</script>
<style lang="scss" scoped>
	.empty-state-container {
		min-height: 300rpx;

		.uv-button {
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-2rpx);
				box-shadow: 0 4rpx 12rpx rgba(32, 165, 10, 0.3);
			}
		}
	}
</style>
