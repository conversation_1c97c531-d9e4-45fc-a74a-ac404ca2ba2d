# CustomDialog 组件文档

## 组件介绍

CustomDialog 是一个基于 uni-app 的自定义对话框组件，提供了灵活的内容展示、丰富的配置选项和完善的交互逻辑。

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| modelValue | Boolean | false | 控制弹窗显示 |
| title | String | '' | 对话框标题 |
| content | Object/Function | null | 内容组件 |
| contentProps | Object | {} | 传递给内容组件的props |
| showConfirm | Boolean | true | 是否显示确认按钮 |
| showCancel | Boolean | true | 是否显示取消按钮 |
| confirmText | String | '确认' | 确认按钮文字 |
| cancelText | String | '取消' | 取消按钮文字 |
| closeOnClickMask | Boolean | false | 点击遮罩是否关闭 |
| contentHeight | String | '40vh' | 内容区域高度（默认为最小高度，内容可撑高） |
| fixedHeight | Boolean | false | 是否固定高度（true时contentHeight为固定高度，false时为最小高度） |
| isMaskClick | Boolean | true | 是否可以点击关闭遮罩 |
| zIndex | Number | 1000 | 控制弹窗的层级 |
| guideTheme | Boolean | false | 是否使用引导主题样式 |
| iconPosition | Object | {top: '84.5rpx', right: '19.5rpx'} | 引导图标位置配置，支持 top、right、bottom、left 四个方向的偏移量，支持 rpx、px 等单位 |

## 事件说明

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| update:modelValue | 弹窗状态更新 | (value: boolean) |
| cancel | 点击取消按钮触发 | (close: Function) |
| confirm | 点击确认按钮触发 | (close: Function) |

## 方法说明

| 方法名 | 说明 |
| --- | --- |
| close | 关闭弹窗 |

## 插槽

| 名称 | 说明 |
| --- | --- |
| default | 当不使用content属性时，可使用默认插槽自定义内容 |

## 使用示例

```vue
<template>
  <custom-dialog
    v-model="showDialog"
    title="提示"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <view class="dialog-content-custom">这是一个自定义内容</view>
  </custom-dialog>
</template>

<script setup>
import { ref } from 'vue'

const showDialog = ref(false)

const handleConfirm = (close) => {
  console.log('确认')
  close()
}

const handleCancel = (close) => {
  console.log('取消')
  close()
}
</script>
```

## 使用内容组件示例

```vue
<template>
  <custom-dialog
    v-model="showDialog"
    title="提示"
    :content="CustomContent"
    :contentProps="{ message: '这是传递给内容组件的数据' }"
  />
</template>

<script setup>
import { ref } from 'vue'
import CustomContent from './CustomContent.vue'

const showDialog = ref(false)
</script>
```

## 引导主题使用示例

```vue
<template>
  <custom-dialog
    v-model="showGuideDialog"
    title="引导提示"
    :guide-theme="true"
    :icon-position="{
      top: '100rpx',
      right: '50rpx'
    }"
    @confirm="handleGuideConfirm"
    @cancel="handleGuideCancel"
  >
    <view class="guide-content">
      这是一个引导主题的对话框，图标位置可以自定义
    </view>
  </custom-dialog>
</template>

<script setup>
import { ref } from 'vue'

const showGuideDialog = ref(false)

const handleGuideConfirm = (close) => {
  console.log('引导确认')
  close()
}

const handleGuideCancel = (close) => {
  console.log('引导取消')
  close()
}
</script>
```

## 注意事项

1. 内容组件可以实现 `onConfirm` 和 `onCancel` 方法来自定义确认和取消逻辑
2. 样式中使用了CSS变量，可通过覆盖这些变量来定制外观：
   - `--bg-color`: 背景颜色
   - `--border-color`: 边框颜色
   - `--text-color-primary`: 主文本颜色
   - `--text-color-secondary`: 次要文本颜色
   - `--bg-color-secondary`: 次要背景颜色
   - `--primary-color`: 主题色 