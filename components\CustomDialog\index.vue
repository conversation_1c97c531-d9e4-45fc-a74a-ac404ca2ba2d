<template>
  <uni-popup
    ref="popup"
    :show="modelValue"
    :type="showType"
    background-color="transparent"
    @change="handleChange"
    :is-mask-click="isMaskClick"
    :style="{ zIndex: zIndex }"
		:class="{ 'guide-theme': guideTheme }"
  >
    <view class="custom-dialog">
      <!-- 标题区域 -->
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
      </view>
      
      <!-- 内容区域 -->
      <scroll-view
        class="dialog-content"
        :style="fixedHeight ? { height: contentHeight } : { minHeight: contentHeight }"
        scroll-y
      >
        <component
          :is="content"
          v-if="content"
          v-bind="contentProps"
          ref="contentRef"
        />
        <slot v-else></slot>
      </scroll-view>
      
      <!-- 按钮区域 -->
      <view class="dialog-footer" v-if="showConfirm || showCancel">
        <button 
          class="dialog-btn cancel" 
          v-if="showCancel"
          :style="cancelStyle"
          :throttleTime="500"
          @click="handleCancel"
        >{{ cancelText }}</button>
        <button 
          class="dialog-btn confirm" 
          v-if="showConfirm"
          :style="confirmStyle"
          :loading="loading"
          :disabled="loading"
          :throttleTime="500"
          @click="handleConfirm"
        >{{ confirmText }}</button>
      </view>
    </view>
		<view v-if="guideTheme" class="guide-top-right-icon" :style="guideIconStyle">
			<uv-icon name="plus-circle" color="var(--text-color-tertiary)" size="32"></uv-icon>
		</view>
		<view v-if="guideTheme" class="guide-bottom-close-icon" @click="handleCancel">
			<uv-icon name="close" color="#fff" size="28"></uv-icon>
		</view>
  </uni-popup>
</template>

<script>
import PageContainer from '@/components/PageContainer/index.vue'
export default {
  name: 'CustomDialog'
}
</script>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue'
import { $t } from '@/locale/index.js'
import { throttle } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common.js'

const props = defineProps({
  // 控制弹窗显示
  modelValue: {
    type: Boolean,
    default: false
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 内容组件
  content: {
    type: [Object, Function],
    default: null
  },
  // 传递给内容组件的props
  contentProps: {
    type: Object,
    default: () => ({})
  },
  // 是否显示确认按钮
  showConfirm: {
    type: Boolean,
    default: true
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  },
  // 确认按钮文字
  confirmText: {
    type: String,
    default: $t('common.confirm')
  },
  // 取消按钮文字
  cancelText: {
    type: String,
    default: $t('common.cancel')
  },
  // 内容区域高度
  contentHeight: {
    type: String,
    default: '40vh'
  },
  // 是否固定高度
  fixedHeight: {
    type: Boolean,
    default: false
  },
  // 是否可以点击关闭遮罩
  isMaskClick: {
    type: Boolean,
    default: true
  },
  // 控制弹窗的层级
  zIndex: {
    type: Number,
    default: 1000
  },
  // 确认按钮样式
  confirmStyle: {
    type: Object,
    default: () => ({})
  },
  // 取消按钮样式
  cancelStyle: {
    type: Object,
    default: () => ({})
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
	},
	// 弹窗显示位置
	showType: {
		type: String,
		default: 'center'
	},
  // 是否使用引导主题样式
  guideTheme: {
    type: Boolean,
    default: false
  },
  // 引导图标位置配置，支持 top、right、bottom、left 四个方向的偏移量，支持 rpx、px 等单位
  iconPosition: {
    type: Object,
    default: () => ({
      top: '84.5rpx',
      right: '19.5rpx'
    })
  }
})

const emit = defineEmits(['update:modelValue', 'cancel', 'confirm'])
const popup = ref(null)
const contentRef = ref(null)

// 计算引导图标的动态样式
const guideIconStyle = computed(() => {
  const style = {}

  // 应用位置配置
  if (props.iconPosition.top !== undefined) {
    style.top = props.iconPosition.top
  }
  if (props.iconPosition.right !== undefined) {
    style.right = props.iconPosition.right
  }
  if (props.iconPosition.bottom !== undefined) {
    style.bottom = props.iconPosition.bottom
  }
  if (props.iconPosition.left !== undefined) {
    style.left = props.iconPosition.left
  }

  return style
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    popup.value?.open()
  } else {
    popup.value?.close()
  }
})

// 处理弹窗状态变化
const handleChange = (e) => {
  if (!e.show && props.modelValue) {
    emit('update:modelValue', false)
  }
}

// 关闭弹窗方法
const close = () => {
  popup.value?.close()
  emit('update:modelValue', false)
}

// 处理取消
const handleCancel = async () => {
  if (contentRef.value?.onCancel) {
    await contentRef.value.onCancel(close)
  } else {
    emit('cancel', close)
    close()
  }
}

// 处理确认
const handleConfirm = throttle(async () => {
  if (contentRef.value?.onConfirm) {
    await contentRef.value.onConfirm(close)
  } else {
    emit('confirm', close)
  }
}, 500)

// 暴露方法给父组件
defineExpose({
  close
})
</script>

<style lang="scss" scoped>
.custom-dialog {
  position: relative;
  width: 80vw;
  max-width: 600rpx;
  background-color: var(--bg-color, #fff);
  border-radius: 16rpx;
  overflow: hidden;
  
  .dialog-header {
    padding: 30rpx;
    text-align: center;
    border-bottom: 1px solid var(--border-color, #eee);
    
    .dialog-title {
      font-size: 32rpx;
      font-weight: 500;
      color: var(--text-color-primary, #333);
    }
  }
  
  .dialog-content {
    padding: 30rpx;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .dialog-footer {
    display: flex;
    padding: 20rpx;
    border-top: 1px solid var(--border-color, #eee);
    
    .dialog-btn {
      flex: 1;
      margin: 0 10rpx;
      min-height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8rpx;
      font-size: 28rpx;
      line-height: 1.2;
      
      &.cancel {
        background-color: var(--bg-color-secondary, #f5f5f5);
        color: var(--text-color-secondary, #666);
      }
      
      &.confirm {
        background-color: var(--primary-color);
        color: #fff;
      }
    }
  }
}
.guide-theme{
	.custom-dialog{
		overflow: visible;
		margin: 0 auto;
		margin-top: 280rpx;
		&::before {
			position: absolute;
			top: -60rpx;
			right: calc(50% - 35vw);
			content: '';
			border-top: 92rpx solid transparent;
			border-bottom: 0 solid transparent;
			border-right: 92rpx solid #fff; 
			transform: skewX(-10deg); 
			z-index: 999;
		}
	}
	.dialog-footer {
		.dialog-btn {
			font-size: 28rpx;
			font-weight: 700;
			border-radius: 36rpx;
			background-color: #CCFFCB!important;
			border: 0!important;
			color: #666!important;
			&::after{
				display: none!important;
			}
		}
	}
}
.guide-top-right-icon {
	position: fixed;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 92rpx;
	line-height: 92rpx;
	width: 92rpx;
	border-radius: 50%;
	background-color: #e2ecee;
}
.guide-bottom-close-icon {
	position: fixed;
	bottom: -120rpx;
	left: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 60rpx;
	line-height: 60rpx;
	width: 60rpx;
	transform: translateX(-50%);
	background-color: transparent;
	border-radius: 50%;
	padding: 5rpx;
	border: 3rpx solid #fff;
	z-index: 5;
	cursor: pointer;
}
// 添加以下全局样式覆盖
:deep(.uni-popup__wrapper) {
  /* #ifndef APP-NVUE */
  display: block;
  /* #endif */
}

:deep(.uni-popup__wrapper-box) {
  /* #ifndef APP-NVUE */
  display: block;
  /* #endif */
  padding: 0;
}
</style> 